import {
  JobQueue,
  <PERSON>QueueOptions,
  KinopoiskJob,
} from "../jobs/PullKinopoiskMarksJob";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresKinopoiskJobQueue implements JobQueue {
  constructor(private pool: ConnectionPool) {}

  async getJobsQueue(options: JobQueueOptions): Promise<KinopoiskJob[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        id: number;
        name: string;
        friends: { limit: number } | null;
        marks: { after: string | null };
      }>(sql`
        WITH
          mark_time_diff AS (
            SELECT
              kinopoisk_movie_mark.user_id as user_id,
              kinopoisk_movie_mark.updated_at as timestamp,
              kinopoisk_movie_mark.updated_at - LAG(kinopoisk_movie_mark.updated_at)  OVER (
                PARTITION BY kinopoisk_movie_mark.user_id
                    ORDER BY kinopoisk_movie_mark.order ASC
                ) AS diff
            FROM
              kinopoisk_movie_mark
            WHERE
              kinopoisk_movie_mark.updated_at > (NOW() - '365 days'::interval)
          ),
          survival AS (
            SELECT
              mark_time_diff.user_id,
              MAX(mark_time_diff."timestamp") as last_mark,
              EXP(
                GREATEST(
                  -700.0, -- Prevent underflow by capping the exponent
                  -1 * EXTRACT(EPOCH FROM (NOW() - MAX(mark_time_diff."timestamp"))) /
                  AVG(EXTRACT(EPOCH FROM mark_time_diff.diff))
                )
              ) as survival -- it's survival function of exponential distribution
            FROM
              mark_time_diff
            WHERE
              mark_time_diff.diff IS NOT NULL
              AND EXTRACT(EPOCH FROM mark_time_diff.diff) > 0
            GROUP BY
              mark_time_diff.user_id
            HAVING
              COUNT(*) >= 5
          ),
          queue AS (
          	SELECT
          		account.kinopoisk_id as kinopoisk_id
          	FROM
          		account

          	UNION

          	SELECT
          		kinopoisk_user.id as kinopoisk_id
          	FROM
	            kinopoisk_user
            LEFT JOIN
              survival
              ON survival.user_id = kinopoisk_user.id
            WHERE
              EXISTS (
                SELECT
                FROM
                  kinopoisk_friend
                JOIN
                  account
                  ON account.kinopoisk_id = kinopoisk_friend.user_id
                WHERE
                  kinopoisk_friend.friend_id = kinopoisk_user.id
              )
              AND (
                (survival.survival < 0.8 AND RANDOM() < survival.survival)
                OR (survival.survival < 0.01 AND RANDOM() < 0.03)
              )

            UNION

          	SELECT
          		kinopoisk_id
          	FROM (
          		SELECT
          			kinopoisk_user.id as kinopoisk_id
          		FROM
          			kinopoisk_user
          		WHERE
          			NOT EXISTS (
          				SELECT 1
          				FROM kinopoisk_movie_mark
          				WHERE kinopoisk_movie_mark.user_id = kinopoisk_user.id
          			)
          		ORDER BY RANDOM()
          		LIMIT 5
          	) random_users_without_marks

            UNION

            SELECT
          		kinopoisk_id
          	FROM (
          		SELECT
          			kinopoisk_user.id as kinopoisk_id
          		FROM
          			kinopoisk_user
          		WHERE
                EXISTS (
                  SELECT
                  FROM
                    account
                  JOIN
                    kinopoisk_friend
                    ON kinopoisk_friend.user_id = account.kinopoisk_id
                  WHERE
                    kinopoisk_friend.friend_id = kinopoisk_user.id
                )
          			AND NOT EXISTS (
          				SELECT 1
          				FROM kinopoisk_movie_mark
          				WHERE kinopoisk_movie_mark.user_id = kinopoisk_user.id
          			)
          		LIMIT 50
          	) kinopoisk_friends_without_marks
          ),
          single_pick AS (
            SELECT
              kinopoisk_user.id as kinopoisk_id
            FROM
              kinopoisk_user
            WHERE
              kinopoisk_user.id = ${options.id}
          )
        SELECT
          kinopoisk_user.id as id,
          kinopoisk_user.name as name,
          CASE
            WHEN ${options.keepPreviousFriends}
            THEN NULL 
            ELSE jsonb_build_object(
              'limit', CASE WHEN account.id IS NOT NULL THEN 100 ELSE 30 END
            )
          END as friends,
          jsonb_build_object(
            'after', (
              SELECT
                MAX(kinopoisk_movie_mark.created_at)
              FROM
                kinopoisk_movie_mark
              WHERE
                kinopoisk_movie_mark.user_id = kinopoisk_user.id
            )
          ) as marks
        FROM
          ${options.id ? sql`single_pick` : sql`queue`} queue
        JOIN
          kinopoisk_user
          ON kinopoisk_user.id = queue.kinopoisk_id
        LEFT JOIN
          account
          ON account.kinopoisk_id = kinopoisk_user.id
      `);

      return rows.map((row) => ({
        id: String(row.id),
        name: row.name,
        friends: row.friends == null ? undefined : { limit: row.friends.limit },
        marks: {
          after:
            row.marks.after == null ? undefined : new Date(row.marks.after),
        },
      }));
    });
  }
}
